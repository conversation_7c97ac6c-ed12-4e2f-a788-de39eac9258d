// Configuration file for Wesotronic AV Projects
const CONFIG = {
    // LinkedIn API Configuration
    linkedin: {
        // LinkedIn API credentials (to be configured)
        clientId: 'YOUR_LINKEDIN_CLIENT_ID',
        clientSecret: 'YOUR_LINKEDIN_CLIENT_SECRET',
        redirectUri: 'https://wesotronic.nl/audiovisueel/auth/linkedin',
        
        // LinkedIn Company Page ID for Wesotronic
        companyId: 'YOUR_COMPANY_ID',
        
        // API endpoints
        endpoints: {
            companyPosts: 'https://api.linkedin.com/v2/shares',
            companyInfo: 'https://api.linkedin.com/v2/companies',
            media: 'https://api.linkedin.com/v2/assets'
        },
        
        // Scopes needed for accessing company posts
        scopes: [
            'r_organization_social',
            'r_basicprofile',
            'r_organization_admin'
        ]
    },
    
    // Cache settings
    cache: {
        // Cache duration in milliseconds (1 hour)
        duration: 60 * 60 * 1000,
        
        // Local storage keys
        keys: {
            projects: 'wesotronic_projects',
            lastUpdate: 'wesotronic_last_update',
            accessToken: 'linkedin_access_token'
        }
    },
    
    // Project filtering and categorization
    projects: {
        // Keywords to identify project categories from LinkedIn posts
        categoryKeywords: {
            conferentie: [
                'conferentieruimte', 'vergaderruimte', 'boardroom', 
                'meeting room', 'conference', 'vergadering'
            ],
            evenement: [
                'evenement', 'event', 'show', 'concert', 'festival',
                'productlancering', 'launch', 'gala', 'awards'
            ],
            onderwijs: [
                'onderwijs', 'education', 'school', 'universiteit',
                'college', 'training', 'cursus', 'les'
            ],
            corporate: [
                'corporate', 'kantoor', 'office', 'headquarters',
                'bedrijf', 'company', 'organisatie'
            ]
        },
        
        // Default images for categories when no image is available
        defaultImages: {
            conferentie: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=500&h=300&fit=crop',
            evenement: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=500&h=300&fit=crop',
            onderwijs: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=500&h=300&fit=crop',
            corporate: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=500&h=300&fit=crop'
        },
        
        // Maximum number of projects to display per page
        perPage: 9,
        
        // Maximum description length for project cards
        maxDescriptionLength: 150
    },
    
    // API rate limiting
    rateLimit: {
        // Maximum requests per hour
        maxRequestsPerHour: 100,
        
        // Delay between requests in milliseconds
        requestDelay: 1000
    },
    
    // Error handling
    errors: {
        // Retry attempts for failed API calls
        maxRetries: 3,
        
        // Retry delay in milliseconds
        retryDelay: 2000,
        
        // Fallback to demo data if API fails
        useFallback: true
    },
    
    // Analytics and tracking
    analytics: {
        // Google Analytics tracking ID (if needed)
        googleAnalyticsId: 'GA_TRACKING_ID',
        
        // Track project views and interactions
        trackInteractions: true
    },
    
    // SEO and metadata
    seo: {
        // Default meta description
        defaultDescription: 'Ontdek onze professionele audiovisuele projecten en referenties. Van conferentieruimtes tot evenementen - bekijk onze expertise in AV-technologie.',
        
        // Default keywords
        defaultKeywords: 'audiovisueel, AV, projecten, referenties, conferentieruimtes, evenementen, Wesotronic',
        
        // Open Graph image
        ogImage: 'https://wesotronic.nl/images/og-image.jpg'
    },
    
    // Contact information
    contact: {
        email: '<EMAIL>',
        phone: '+31 (0)20 123 4567',
        address: {
            street: 'Voorbeeldstraat 123',
            city: 'Amsterdam',
            postalCode: '1234 AB',
            country: 'Nederland'
        },
        
        // Social media links
        social: {
            linkedin: 'https://www.linkedin.com/company/wesotronic',
            instagram: 'https://www.instagram.com/wesotronic',
            facebook: 'https://www.facebook.com/wesotronic'
        }
    },
    
    // Feature flags
    features: {
        // Enable/disable LinkedIn integration
        linkedinIntegration: true,
        
        // Enable/disable search functionality
        searchEnabled: true,
        
        // Enable/disable project filtering
        filteringEnabled: true,
        
        // Enable/disable lazy loading
        lazyLoading: true,
        
        // Enable/disable animations
        animations: true
    },
    
    // Development settings
    development: {
        // Enable debug logging
        debug: false,
        
        // Use mock data instead of API
        useMockData: true,
        
        // API base URL for development
        apiBaseUrl: 'http://localhost:3000/api'
    }
};

// LinkedIn API Helper Functions
const LinkedInAPI = {
    // Get access token for LinkedIn API
    async getAccessToken() {
        const cachedToken = localStorage.getItem(CONFIG.cache.keys.accessToken);
        if (cachedToken) {
            const tokenData = JSON.parse(cachedToken);
            if (Date.now() < tokenData.expiresAt) {
                return tokenData.token;
            }
        }
        
        // If no valid cached token, redirect to LinkedIn OAuth
        return this.initiateOAuth();
    },
    
    // Initiate LinkedIn OAuth flow
    initiateOAuth() {
        const params = new URLSearchParams({
            response_type: 'code',
            client_id: CONFIG.linkedin.clientId,
            redirect_uri: CONFIG.linkedin.redirectUri,
            scope: CONFIG.linkedin.scopes.join(' ')
        });
        
        const authUrl = `https://www.linkedin.com/oauth/v2/authorization?${params}`;
        
        // In a real implementation, this would redirect to LinkedIn
        console.log('LinkedIn OAuth URL:', authUrl);
        return null;
    },
    
    // Fetch company posts from LinkedIn
    async fetchCompanyPosts(accessToken) {
        try {
            const response = await fetch(`${CONFIG.linkedin.endpoints.companyPosts}?q=owners&owners=urn:li:organization:${CONFIG.linkedin.companyId}`, {
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                throw new Error(`LinkedIn API error: ${response.status}`);
            }
            
            const data = await response.json();
            return this.processLinkedInPosts(data);
            
        } catch (error) {
            console.error('Error fetching LinkedIn posts:', error);
            throw error;
        }
    },
    
    // Process LinkedIn posts into project format
    processLinkedInPosts(data) {
        if (!data.elements) return [];
        
        return data.elements.map(post => {
            const text = post.specificContent?.['com.linkedin.ugc.ShareContent']?.shareCommentary?.text || '';
            const category = this.detectCategory(text);
            
            return {
                id: post.id,
                title: this.extractTitle(text),
                description: this.extractDescription(text),
                category: category,
                image: this.extractImage(post) || CONFIG.projects.defaultImages[category],
                date: new Date(post.created.time).toISOString().split('T')[0],
                client: this.extractClient(text),
                location: this.extractLocation(text),
                linkedinUrl: `https://www.linkedin.com/feed/update/${post.id}`
            };
        }).filter(project => project.title && project.description);
    },
    
    // Detect project category from post text
    detectCategory(text) {
        const lowerText = text.toLowerCase();
        
        for (const [category, keywords] of Object.entries(CONFIG.projects.categoryKeywords)) {
            if (keywords.some(keyword => lowerText.includes(keyword))) {
                return category;
            }
        }
        
        return 'corporate'; // Default category
    },
    
    // Extract project title from post text
    extractTitle(text) {
        // Look for patterns like "Project: Title" or first sentence
        const lines = text.split('\n').filter(line => line.trim());
        if (lines.length > 0) {
            let title = lines[0];
            if (title.includes(':')) {
                title = title.split(':')[1].trim();
            }
            return title.substring(0, 100); // Limit title length
        }
        return 'Audiovisueel Project';
    },
    
    // Extract project description from post text
    extractDescription(text) {
        const lines = text.split('\n').filter(line => line.trim());
        if (lines.length > 1) {
            return lines.slice(1).join(' ').substring(0, CONFIG.projects.maxDescriptionLength);
        }
        return text.substring(0, CONFIG.projects.maxDescriptionLength);
    },
    
    // Extract image from LinkedIn post
    extractImage(post) {
        const content = post.specificContent?.['com.linkedin.ugc.ShareContent'];
        if (content?.media && content.media.length > 0) {
            const media = content.media[0];
            if (media.media && media.media['com.linkedin.digitalmedia.MediaArtifact']) {
                // Return the media URL if available
                return media.media['com.linkedin.digitalmedia.MediaArtifact'].downloadUrl;
            }
        }
        return null;
    },
    
    // Extract client name from post text
    extractClient(text) {
        // Look for patterns like "voor [Client]" or "@Client"
        const clientMatch = text.match(/(?:voor|@)\s+([A-Z][a-zA-Z\s&]+)/);
        if (clientMatch) {
            return clientMatch[1].trim();
        }
        return 'Vertrouwelijke klant';
    },
    
    // Extract location from post text
    extractLocation(text) {
        // Look for Dutch city names or location indicators
        const locationMatch = text.match(/(?:in|te|@)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)/);
        if (locationMatch) {
            return locationMatch[1].trim();
        }
        return 'Nederland';
    }
};

// Make CONFIG and LinkedInAPI available globally
window.CONFIG = CONFIG;
window.LinkedInAPI = LinkedInAPI;
