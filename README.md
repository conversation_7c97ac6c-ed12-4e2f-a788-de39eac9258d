# Wesotronic Audiovisueel Referentiepagina

Een professionele referentiepagina die automatisch projecten en content van LinkedIn haalt om altijd up-to-date te blij<PERSON> met de nieuwste Wesotronic AV-projecten.

## Features

- 🎨 **Modern, responsive design** - Werkt perfect op desktop, tablet en mobiel
- 🔗 **LinkedIn integratie** - Automatisch ophalen van posts en projecten
- 🔍 **Zoe<PERSON> en filter functionaliteit** - Eenvoudig projecten vinden
- ⚡ **Performance optimalisatie** - Lazy loading en caching
- 📱 **Mobile-first design** - Optimaal voor alle apparaten
- 🎯 **SEO geoptimaliseerd** - Meta tags en structured data
- ♿ **Toegankelijk** - WCAG richtlijnen gevolgd

## Bestanden Overzicht

```
/
├── index.html          # Hoofdpagina met HTML structuur
├── styles.css          # Complete styling en responsive design
├── script.js           # JavaScript functionaliteit en LinkedIn integratie
├── config.js           # Configuratie voor API's en instellingen
└── README.md           # Deze documentatie
```

## LinkedIn API Setup

### Stap 1: LinkedIn Developer Account

1. Ga naar [LinkedIn Developer Portal](https://developer.linkedin.com/)
2. Maak een nieuwe app aan voor Wesotronic
3. Noteer de `Client ID` en `Client Secret`

### Stap 2: Configuratie

Bewerk `config.js` en vul de LinkedIn credentials in:

```javascript
linkedin: {
    clientId: 'JOUW_LINKEDIN_CLIENT_ID',
    clientSecret: 'JOUW_LINKEDIN_CLIENT_SECRET',
    companyId: 'WESOTRONIC_COMPANY_ID',
    redirectUri: 'https://wesotronic.nl/audiovisueel/auth/linkedin'
}
```

### Stap 3: OAuth Setup

1. Voeg de redirect URI toe in je LinkedIn app settings
2. Vraag de volgende permissions aan:
   - `r_organization_social` - Voor company posts
   - `r_basicprofile` - Voor basis profiel info
   - `r_organization_admin` - Voor company admin rechten

### Stap 4: Company ID Vinden

De LinkedIn Company ID kun je vinden via:
```
https://www.linkedin.com/company/wesotronic
```
De ID staat in de URL of gebruik de LinkedIn API om deze op te halen.

## Installatie

### Optie 1: Direct uploaden
1. Upload alle bestanden naar je webserver
2. Zorg dat de bestanden toegankelijk zijn via `wesotronic.nl/audiovisueel/`
3. Configureer de LinkedIn API credentials

### Optie 2: Met webserver
```bash
# Voor development met Python
python -m http.server 8000

# Voor development met Node.js
npx serve .

# Voor development met PHP
php -S localhost:8000
```

## Configuratie Opties

### Cache Instellingen
```javascript
cache: {
    duration: 60 * 60 * 1000, // 1 uur cache
    keys: {
        projects: 'wesotronic_projects',
        lastUpdate: 'wesotronic_last_update'
    }
}
```

### Project Categorieën
De pagina categoriseert automatisch projecten op basis van keywords:
- **Conferentieruimtes**: conferentieruimte, vergaderruimte, meeting room
- **Evenementen**: evenement, event, show, concert, festival
- **Onderwijs**: onderwijs, education, school, universiteit
- **Corporate**: corporate, kantoor, office, headquarters

### Feature Flags
```javascript
features: {
    linkedinIntegration: true,  // LinkedIn API aan/uit
    searchEnabled: true,        // Zoekfunctie aan/uit
    filteringEnabled: true,     // Filter functie aan/uit
    lazyLoading: true,         // Lazy loading aan/uit
    animations: true           // Animaties aan/uit
}
```

## Automatische Updates

De pagina update automatisch elke uur door:
1. LinkedIn API te checken voor nieuwe posts
2. Posts te analyseren op project-gerelateerde content
3. Automatisch categoriseren op basis van keywords
4. Afbeeldingen en metadata extraheren
5. Cache bijwerken voor snelle laadtijden

## Fallback Systeem

Als de LinkedIn API niet beschikbaar is:
- Toont demo projecten
- Gebruikt gecachte data indien beschikbaar
- Geeft duidelijke feedback aan gebruikers
- Blijft volledig functioneel

## Browser Ondersteuning

- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)

## Performance

- **Lazy loading** voor afbeeldingen
- **Local storage caching** voor API responses
- **CSS Grid** voor efficiënte layouts
- **Intersection Observer** voor scroll animaties
- **Debounced search** voor betere UX

## SEO Optimalisatie

- Semantic HTML5 markup
- Open Graph meta tags
- Twitter Card support
- Structured data (JSON-LD)
- Optimized meta descriptions
- Proper heading hierarchy

## Toegankelijkheid

- ARIA labels en roles
- Keyboard navigatie support
- Screen reader vriendelijk
- High contrast ondersteuning
- Focus indicators
- Alt teksten voor afbeeldingen

## Aanpassingen

### Styling Aanpassen
Bewerk `styles.css` en pas de CSS variabelen aan:
```css
:root {
    --primary-color: #0066cc;
    --accent-color: #ff6b35;
    --text-primary: #2c3e50;
    /* etc... */
}
```

### Nieuwe Categorieën Toevoegen
Voeg keywords toe in `config.js`:
```javascript
categoryKeywords: {
    nieuwecategorie: ['keyword1', 'keyword2', 'keyword3']
}
```

### Demo Data Aanpassen
Bewerk de `getDemoProjects()` functie in `script.js` om andere voorbeeldprojecten te tonen.

## Troubleshooting

### LinkedIn API Errors
- Check of credentials correct zijn ingevuld
- Controleer of redirect URI klopt
- Zorg dat permissions zijn goedgekeurd

### Geen Projecten Zichtbaar
- Check browser console voor errors
- Controleer of demo data wordt geladen
- Test met development mode aan

### Performance Issues
- Controleer afbeelding groottes
- Test cache functionaliteit
- Monitor API rate limits

## Support

Voor vragen of problemen:
- Check de browser console voor error messages
- Test eerst met demo data
- Controleer LinkedIn API status
- Bekijk network tab voor API calls

## Licentie

© 2024 Wesotronic. Alle rechten voorbehouden.
