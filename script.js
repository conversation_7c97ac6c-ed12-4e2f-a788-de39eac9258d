// Main application class
class WesotronicoAVProjects {
    constructor() {
        this.projects = [];
        this.filteredProjects = [];
        this.currentFilter = 'all';
        this.currentPage = 1;
        this.projectsPerPage = 9;
        this.searchTerm = '';
        this.isLoading = false;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.animateCounters();
        this.loadProjects();
        this.setupIntersectionObserver();
    }

    setupEventListeners() {
        // Filter buttons
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.handleFilterChange(e.target.dataset.filter);
            });
        });

        // Search input
        const searchInput = document.getElementById('searchInput');
        searchInput.addEventListener('input', (e) => {
            this.handleSearch(e.target.value);
        });

        // Load more button
        const loadMoreBtn = document.getElementById('loadMoreBtn');
        loadMoreBtn.addEventListener('click', () => {
            this.loadMoreProjects();
        });

        // Mobile menu toggle
        const mobileToggle = document.querySelector('.mobile-menu-toggle');
        if (mobileToggle) {
            mobileToggle.addEventListener('click', this.toggleMobileMenu);
        }
    }

    async loadProjects() {
        this.showLoading(true);
        
        try {
            // Try to load from LinkedIn API first
            const linkedinProjects = await this.fetchLinkedInProjects();
            
            if (linkedinProjects && linkedinProjects.length > 0) {
                this.projects = linkedinProjects;
            } else {
                // Fallback to demo data if LinkedIn API fails
                this.projects = this.getDemoProjects();
            }
            
            this.filteredProjects = [...this.projects];
            this.renderProjects();
            
        } catch (error) {
            console.error('Error loading projects:', error);
            // Use demo data as fallback
            this.projects = this.getDemoProjects();
            this.filteredProjects = [...this.projects];
            this.renderProjects();
        } finally {
            this.showLoading(false);
        }
    }

    async fetchLinkedInProjects() {
        // Check if we have cached data that's less than 1 hour old
        const cachedData = this.getCachedData();
        if (cachedData) {
            return cachedData;
        }

        try {
            // LinkedIn API integration would go here
            // For now, we'll simulate the API call and return demo data
            await this.delay(2000); // Simulate API delay
            
            const projects = this.getDemoProjects();
            this.setCachedData(projects);
            return projects;
            
        } catch (error) {
            console.error('LinkedIn API error:', error);
            return null;
        }
    }

    getCachedData() {
        try {
            const cached = localStorage.getItem('wesotronic_projects');
            if (cached) {
                const data = JSON.parse(cached);
                const oneHour = 60 * 60 * 1000;
                if (Date.now() - data.timestamp < oneHour) {
                    return data.projects;
                }
            }
        } catch (error) {
            console.error('Error reading cached data:', error);
        }
        return null;
    }

    setCachedData(projects) {
        try {
            const data = {
                projects: projects,
                timestamp: Date.now()
            };
            localStorage.setItem('wesotronic_projects', JSON.stringify(data));
        } catch (error) {
            console.error('Error caching data:', error);
        }
    }

    getDemoProjects() {
        return [
            {
                id: 1,
                title: "Conferentieruimte Modernisering - TechCorp",
                description: "Complete audiovisuele upgrade van 5 conferentieruimtes met 4K displays, draadloze presentatiesystemen en geïntegreerde videoconferencing.",
                category: "conferentie",
                image: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=500&h=300&fit=crop",
                date: "2024-01-15",
                client: "TechCorp B.V.",
                location: "Amsterdam"
            },
            {
                id: 2,
                title: "Hybride Evenement Setup - Innovation Summit",
                description: "Professionele AV-ondersteuning voor hybride evenement met 500 fysieke en 2000 online deelnemers, inclusief live streaming en interactieve elementen.",
                category: "evenement",
                image: "https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=500&h=300&fit=crop",
                date: "2024-02-20",
                client: "Innovation Hub",
                location: "Rotterdam"
            },
            {
                id: 3,
                title: "Universiteitscampus AV-Infrastructuur",
                description: "Implementatie van moderne AV-systemen in 20 collegezalen en auditoriums, inclusief opname- en streaming-mogelijkheden voor online onderwijs.",
                category: "onderwijs",
                image: "https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=500&h=300&fit=crop",
                date: "2024-03-10",
                client: "Universiteit van Amsterdam",
                location: "Amsterdam"
            },
            {
                id: 4,
                title: "Corporate Headquarters AV-Integratie",
                description: "Volledige AV-integratie in nieuw hoofdkantoor, inclusief digital signage, vergaderruimte-technologie en centrale controle-systemen.",
                category: "corporate",
                image: "https://images.unsplash.com/photo-1497366216548-37526070297c?w=500&h=300&fit=crop",
                date: "2024-01-30",
                client: "Global Solutions Inc.",
                location: "Den Haag"
            },
            {
                id: 5,
                title: "Productlancering Event - Fashion Week",
                description: "Spectaculaire AV-productie voor fashion show met LED-walls, dynamische belichting en surround sound systeem.",
                category: "evenement",
                image: "https://images.unsplash.com/photo-1469371670807-013ccf25f16a?w=500&h=300&fit=crop",
                date: "2024-02-05",
                client: "Fashion Forward",
                location: "Amsterdam"
            },
            {
                id: 6,
                title: "Medische Training Faciliteit",
                description: "Geavanceerde AV-systemen voor medische simulatieruimtes, inclusief high-definition camera's en real-time streaming voor training doeleinden.",
                category: "onderwijs",
                image: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=500&h=300&fit=crop",
                date: "2024-03-25",
                client: "Medical Training Center",
                location: "Utrecht"
            }
        ];
    }

    handleFilterChange(filter) {
        this.currentFilter = filter;
        this.currentPage = 1;
        
        // Update active filter button
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-filter="${filter}"]`).classList.add('active');
        
        this.applyFilters();
    }

    handleSearch(searchTerm) {
        this.searchTerm = searchTerm.toLowerCase();
        this.currentPage = 1;
        this.applyFilters();
    }

    applyFilters() {
        let filtered = [...this.projects];
        
        // Apply category filter
        if (this.currentFilter !== 'all') {
            filtered = filtered.filter(project => project.category === this.currentFilter);
        }
        
        // Apply search filter
        if (this.searchTerm) {
            filtered = filtered.filter(project => 
                project.title.toLowerCase().includes(this.searchTerm) ||
                project.description.toLowerCase().includes(this.searchTerm) ||
                project.client.toLowerCase().includes(this.searchTerm)
            );
        }
        
        this.filteredProjects = filtered;
        this.renderProjects();
    }

    renderProjects() {
        const grid = document.getElementById('projectsGrid');
        const projectsToShow = this.filteredProjects.slice(0, this.currentPage * this.projectsPerPage);
        
        grid.innerHTML = '';
        
        projectsToShow.forEach((project, index) => {
            const projectCard = this.createProjectCard(project);
            projectCard.style.animationDelay = `${(index % this.projectsPerPage) * 0.1}s`;
            grid.appendChild(projectCard);
        });
        
        this.updateLoadMoreButton();
    }

    createProjectCard(project) {
        const card = document.createElement('div');
        card.className = 'project-card';
        card.dataset.category = project.category;
        
        const formattedDate = new Date(project.date).toLocaleDateString('nl-NL', {
            year: 'numeric',
            month: 'long'
        });
        
        card.innerHTML = `
            <div class="project-image">
                <img src="${project.image}" alt="${project.title}" loading="lazy">
                <div class="project-category">${this.getCategoryLabel(project.category)}</div>
            </div>
            <div class="project-content">
                <h3 class="project-title">${project.title}</h3>
                <p class="project-description">${project.description}</p>
                <div class="project-meta">
                    <span class="project-date">${formattedDate}</span>
                    <a href="#" class="project-link">Meer details</a>
                </div>
            </div>
        `;
        
        return card;
    }

    getCategoryLabel(category) {
        const labels = {
            'conferentie': 'Conferentieruimtes',
            'evenement': 'Evenementen',
            'onderwijs': 'Onderwijs',
            'corporate': 'Corporate'
        };
        return labels[category] || category;
    }

    loadMoreProjects() {
        this.currentPage++;
        this.renderProjects();
    }

    updateLoadMoreButton() {
        const loadMoreBtn = document.getElementById('loadMoreBtn');
        const totalShown = this.currentPage * this.projectsPerPage;
        
        if (totalShown >= this.filteredProjects.length) {
            loadMoreBtn.style.display = 'none';
        } else {
            loadMoreBtn.style.display = 'block';
        }
    }

    showLoading(show) {
        const loadingContainer = document.getElementById('loadingContainer');
        const projectsSection = document.getElementById('projectsSection');
        
        if (show) {
            loadingContainer.classList.add('show');
            projectsSection.style.display = 'none';
        } else {
            loadingContainer.classList.remove('show');
            projectsSection.style.display = 'block';
        }
    }

    animateCounters() {
        const counters = document.querySelectorAll('.stat-number');
        
        counters.forEach(counter => {
            const target = parseInt(counter.dataset.count);
            const duration = 2000;
            const step = target / (duration / 16);
            let current = 0;
            
            const timer = setInterval(() => {
                current += step;
                if (current >= target) {
                    counter.textContent = target + '+';
                    clearInterval(timer);
                } else {
                    counter.textContent = Math.floor(current);
                }
            }, 16);
        });
    }

    setupIntersectionObserver() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, { threshold: 0.1 });

        // Observe project cards as they're added
        const observeCards = () => {
            document.querySelectorAll('.project-card').forEach(card => {
                observer.observe(card);
            });
        };

        // Initial observation
        setTimeout(observeCards, 100);
        
        // Re-observe when new cards are added
        const originalRenderProjects = this.renderProjects.bind(this);
        this.renderProjects = function() {
            originalRenderProjects();
            setTimeout(observeCards, 100);
        };
    }

    toggleMobileMenu() {
        const nav = document.querySelector('.nav');
        nav.classList.toggle('mobile-open');
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new WesotronicoAVProjects();
});

// Add smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});
